export type NoteName = 'C' | 'D' | 'E' | 'F' | 'G' | 'A' | 'B';

export type Accidental = 'natural' | 'sharp' | 'flat';

export interface Note {
  name: NoteName;
  accidental: Accidental;
  octave: number;
  position: number; // Position on the staff (0 = middle line)
}

export interface GameSettings {
  displayTime: number; // Time in seconds to show notes
  numberOfNotes: number; // Number of notes to display
  includeAccidentals: boolean;
  clef: 'treble' | 'bass';
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface GameState {
  currentNotes: Note[];
  userAnswers: string[];
  score: number;
  totalQuestions: number;
  isShowingNotes: boolean;
  isGameActive: boolean;
  timeRemaining: number;
}

export interface StaffDimensions {
  width: number;
  height: number;
  lineSpacing: number;
  noteSpacing: number;
}
