import { Note, NoteName, Accidental } from '../types/music';

// Treble clef note positions (relative to staff lines)
// VexFlow uses standard music notation positioning
export const TREBLE_CLEF_POSITIONS: Record<string, number> = {
  'C4': -6, 'D4': -5, 'E4': -4, 'F4': -3, 'G4': -2, 'A4': -1, 'B4': 0,
  'C5': 1, 'D5': 2, 'E5': 3, 'F5': 4, 'G5': 5, 'A5': 6, 'B5': 7,
  'C6': 8, 'D6': 9, 'E6': 10
};

// VexFlow compatible note names for easy conversion
export const VEXFLOW_NOTE_MAPPING: Record<string, string> = {
  'C': 'c', 'D': 'd', 'E': 'e', 'F': 'f', 'G': 'g', 'A': 'a', 'B': 'b'
};

export const NOTE_NAMES: NoteName[] = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];

export const ACCIDENTALS: Accidental[] = ['natural', 'sharp', 'flat'];

export function getNoteDisplayName(note: Note): string {
  const accidentalSymbol = {
    natural: '',
    sharp: '♯',
    flat: '♭'
  };
  
  return `${note.name}${accidentalSymbol[note.accidental]}${note.octave}`;
}

export function getPositionForNote(noteName: string): number {
  return TREBLE_CLEF_POSITIONS[noteName] || 0;
}

export function isNoteOnLine(position: number): boolean {
  // Staff lines are at positions: -4, -2, 0, 2, 4
  // Ledger lines extend this pattern
  return position % 2 === 0;
}

export function needsLedgerLine(position: number): boolean {
  // Ledger lines needed above staff (position > 4) or below staff (position < -4)
  return Math.abs(position) > 4;
}

export function getLedgerLines(position: number): number[] {
  const lines: number[] = [];
  
  if (position > 4) {
    // Above staff
    for (let i = 6; i <= position; i += 2) {
      lines.push(i);
    }
  } else if (position < -4) {
    // Below staff
    for (let i = -6; i >= position; i -= 2) {
      lines.push(i);
    }
  }
  
  return lines;
}

// Enharmonic equivalents mapping
const ENHARMONIC_EQUIVALENTS: Record<string, string[]> = {
  'C': ['C', 'B♯'],
  'C♯': ['C♯', 'D♭'],
  'D': ['D'],
  'D♯': ['D♯', 'E♭'],
  'E': ['E', 'F♭'],
  'F': ['F', 'E♯'],
  'F♯': ['F♯', 'G♭'],
  'G': ['G'],
  'G♯': ['G♯', 'A♭'],
  'A': ['A'],
  'A♯': ['A♯', 'B♭'],
  'B': ['B', 'C♭'],
};

function areEnharmonicEquivalents(note1: string, note2: string): boolean {
  // Remove octave numbers and normalize
  const clean1 = note1.replace(/\d+$/, '').trim();
  const clean2 = note2.replace(/\d+$/, '').trim();

  // Check if they're in the same enharmonic group
  for (const equivalents of Object.values(ENHARMONIC_EQUIVALENTS)) {
    if (equivalents.includes(clean1) && equivalents.includes(clean2)) {
      return true;
    }
  }

  return false;
}

export function validateNoteAnswer(userAnswer: string, correctNote: Note): boolean {
  const correctAnswer = getNoteDisplayName(correctNote);
  const normalizedUserAnswer = userAnswer.trim().toUpperCase();
  const normalizedCorrectAnswer = correctAnswer.toUpperCase();

  // Direct match (with or without octave)
  const userWithoutOctave = normalizedUserAnswer.replace(/\d+$/, '');
  const correctWithoutOctave = normalizedCorrectAnswer.replace(/\d+$/, '');

  if (normalizedUserAnswer === normalizedCorrectAnswer ||
      userWithoutOctave === correctWithoutOctave) {
    return true;
  }

  // Check enharmonic equivalents
  return areEnharmonicEquivalents(normalizedUserAnswer, normalizedCorrectAnswer);
}
