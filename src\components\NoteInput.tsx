import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { Note } from '../types/music';
import { getNoteDisplayName } from '../utils/musicTheory';

interface NoteInputProps {
  expectedNotes: Note[];
  onSubmit: (answers: string[]) => void;
  disabled?: boolean;
}

const NoteInput: React.FC<NoteInputProps> = ({ expectedNotes, onSubmit, disabled = false }) => {
  const [selectedAnswers, setSelectedAnswers] = useState<string[]>(
    new Array(expectedNotes.length).fill('')
  );
  
  // Available note options (including all enharmonic equivalents)
  const noteOptions = [
    'C', 'B♯', 'C♯', 'D♭', 'D', 'D♯', 'E♭', 'E', 'E♯', 'F♭', 'F', 'F♯', 'G♭', 'G', 'G♯', 'A♭', 'A', 'A♯', 'B♭', 'B', 'C♭'
  ];
  
  const handleNoteSelection = (noteIndex: number, noteName: string) => {
    const newAnswers = [...selectedAnswers];
    newAnswers[noteIndex] = noteName;
    setSelectedAnswers(newAnswers);
  };
  
  const handleSubmit = () => {
    onSubmit(selectedAnswers);
  };
  
  const isSubmitEnabled = selectedAnswers.every(answer => answer !== '') && !disabled;
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Enter the note names:</Text>
      
      {expectedNotes.map((_, index) => (
        <View key={index} style={styles.noteInputContainer}>
          <Text style={styles.noteLabel}>Note {index + 1}:</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.optionsContainer}
          >
            {noteOptions.map((option) => (
              <TouchableOpacity
                key={option}
                style={[
                  styles.noteOption,
                  selectedAnswers[index] === option && styles.selectedOption
                ]}
                onPress={() => handleNoteSelection(index, option)}
                disabled={disabled}
              >
                <Text style={[
                  styles.noteOptionText,
                  selectedAnswers[index] === option && styles.selectedOptionText
                ]}>
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      ))}
      
      <TouchableOpacity
        style={[
          styles.submitButton,
          !isSubmitEnabled && styles.submitButtonDisabled
        ]}
        onPress={handleSubmit}
        disabled={!isSubmitEnabled}
      >
        <Text style={[
          styles.submitButtonText,
          !isSubmitEnabled && styles.submitButtonTextDisabled
        ]}>
          Submit Answers
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    margin: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#333',
  },
  noteInputContainer: {
    marginBottom: 15,
  },
  noteLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#555',
  },
  optionsContainer: {
    flexDirection: 'row',
  },
  noteOption: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 8,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#ddd',
    minWidth: 50,
    alignItems: 'center',
  },
  selectedOption: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  noteOptionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  selectedOptionText: {
    color: '#fff',
  },
  submitButton: {
    backgroundColor: '#28a745',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 10,
    marginTop: 20,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  submitButtonTextDisabled: {
    color: '#999',
  },
});

export default NoteInput;
