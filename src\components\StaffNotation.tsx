import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Text, Platform } from 'react-native';
import { WebView } from 'react-native-webview';
import { Note, StaffDimensions } from '../types/music';
import { getNoteDisplayName } from '../utils/musicTheory';

interface StaffNotationProps {
  notes: Note[];
  dimensions: StaffDimensions;
  showNotes: boolean;
}

const StaffNotation: React.FC<StaffNotationProps> = ({ notes, dimensions, showNotes }) => {
  const { width, height } = dimensions;
  const [webViewError, setWebViewError] = useState<string | null>(null);
  const [vexFlowLoaded, setVexFlowLoaded] = useState(false);
  const notationRef = useRef<HTMLDivElement>(null);

  // Convert our Note objects to VexFlow notation format
  const convertNotesToVexFlow = (notes: Note[]): string[] => {
    return notes.map(note => {
      const noteName = note.name.toLowerCase();
      const accidental = note.accidental === 'sharp' ? '#' : note.accidental === 'flat' ? 'b' : '';
      return `${noteName}${accidental}/${note.octave}`;
    });
  };

  // Web-specific VexFlow implementation
  useEffect(() => {
    if (Platform.OS === 'web' && typeof window !== 'undefined') {
      // Load VexFlow script dynamically for web
      const script = document.createElement('script');
      script.src = 'https://unpkg.com/vexflow@4.2.2/build/cjs/vexflow.js';
      script.onload = () => {
        setVexFlowLoaded(true);
        renderVexFlowWeb();
      };
      script.onerror = () => {
        setWebViewError('Failed to load VexFlow library');
      };
      document.head.appendChild(script);

      return () => {
        document.head.removeChild(script);
      };
    }
  }, []);

  useEffect(() => {
    if (Platform.OS === 'web' && vexFlowLoaded) {
      renderVexFlowWeb();
    }
  }, [notes, showNotes, vexFlowLoaded]);

  const renderVexFlowWeb = () => {
    if (Platform.OS !== 'web' || !vexFlowLoaded || !notationRef.current) return;

    try {
      // Clear previous content
      notationRef.current.innerHTML = '';



      // @ts-ignore - VexFlow is loaded dynamically
      const { Renderer, Stave, StaveNote, Voice, Formatter, Accidental } = Vex.Flow;

      // Create renderer
      const renderer = new Renderer(notationRef.current, Renderer.Backends.SVG);
      renderer.resize(width, height);
      const context = renderer.getContext();

      // Create stave with optimized width
      const staveWidth = Math.max(200, width - 30); // More compact minimum width
      const stave = new Stave(15, 30, staveWidth);
      stave.addClef('treble');
      stave.setContext(context).draw();

      if (showNotes && notes.length > 0) {
        const vexFlowNotes = convertNotesToVexFlow(notes);
        const staveNotes: any[] = [];

        vexFlowNotes.forEach((noteStr, index) => {
          try {
            const originalNote = notes[index];

            // Use natural note name for VexFlow
            const noteName = originalNote.name.toLowerCase();
            const naturalNoteStr = `${noteName}/${originalNote.octave}`;

            const note = new StaveNote({
              clef: 'treble',
              keys: [naturalNoteStr],
              duration: 'q'
            });

            // Add accidentals using addModifier method
            if (originalNote.accidental === 'sharp') {
              note.addModifier(new Accidental('#'), 0);
            } else if (originalNote.accidental === 'flat') {
              note.addModifier(new Accidental('b'), 0);
            }

            staveNotes.push(note);
          } catch (error) {
            console.error('Error creating note:', noteStr, 'Original note:', notes[index], error);
          }
        });

        if (staveNotes.length > 0) {
          // Calculate the correct number of beats based on note count
          // Each quarter note = 1 beat, so we need at least as many beats as notes
          const numBeats = staveNotes.length;

          const voice = new Voice({
            num_beats: numBeats,
            beat_value: 4
          });

          // Add all notes to the voice
          voice.addTickables(staveNotes);

          // Format and render with optimized spacing
          const formatWidth = Math.max(150, staveWidth - 60);
          new Formatter().joinVoices([voice]).format([voice], formatWidth);
          voice.draw(context, stave);
        }
      }
    } catch (error) {
      console.error('VexFlow rendering error:', error);
      setWebViewError(`Rendering error: ${error.message}`);
    }
  };

  // Generate improved HTML content with VexFlow
  const generateVexFlowHTML = () => {
    const vexFlowNotes = showNotes ? convertNotesToVexFlow(notes) : [];

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
        <style>
            body {
                margin: 0;
                padding: 0;
                background-color: white;
                font-family: Arial, sans-serif;
                overflow: hidden;
            }
            #notation {
                width: 100%;
                height: 10vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .error {
                color: red;
                text-align: center;
                padding: 20px;
            }
        </style>
    </head>
    <body>
        <div id="notation"></div>

        <!-- Load VexFlow from CDN -->
        <script src="https://unpkg.com/vexflow@4.2.2/build/cjs/vexflow.js"></script>

        <script>
            try {
                console.log('VexFlow loading...');

                // Wait for VexFlow to load
                if (typeof Vex === 'undefined') {
                    throw new Error('VexFlow failed to load');
                }

                const { Renderer, Stave, StaveNote, Voice, Formatter, Accidental } = Vex.Flow;

                console.log('VexFlow loaded successfully');

                // Create an SVG renderer and attach it to the DIV element
                const div = document.getElementById('notation');
                const renderer = new Renderer(div, Renderer.Backends.SVG);

                // Configure the rendering context
                renderer.resize(${width}, ${height});
                const context = renderer.getContext();
                context.setFont('Arial', 10);

                // Create a stave with optimized width
                const staveWidth = Math.max(200, ${width} - 30);
                const stave = new Stave(15, 30, staveWidth);

                // Add a clef
                stave.addClef('treble');

                // Connect it to the rendering context and draw
                stave.setContext(context).draw();

                console.log('Staff drawn successfully');

                ${showNotes && vexFlowNotes.length > 0 ? `
                // Create the notes
                const notes = [];
                const noteData = ${JSON.stringify(vexFlowNotes)};

                console.log('Creating notes:', noteData);

                const originalNotes = ${JSON.stringify(notes.map(note => ({ name: note.name, accidental: note.accidental, octave: note.octave })))};

                originalNotes.forEach((originalNote, index) => {
                    try {
                        // Use natural note name for VexFlow
                        const noteKey = originalNote.name.toLowerCase();
                        const naturalNoteStr = noteKey + '/' + originalNote.octave;

                        const note = new StaveNote({
                            clef: 'treble',
                            keys: [naturalNoteStr],
                            duration: 'q'
                        });

                        // Add accidentals using addModifier method
                        if (originalNote.accidental === 'sharp') {
                            note.addModifier(new Accidental('#'), 0);
                        } else if (originalNote.accidental === 'flat') {
                            note.addModifier(new Accidental('b'), 0);
                        }

                        notes.push(note);
                    } catch (noteError) {
                        console.error('Error creating note:', originalNote, noteError);
                    }
                });

                if (notes.length > 0) {
                    // Create a voice with correct number of beats
                    const numBeats = notes.length;
                    const voice = new Voice({
                        num_beats: numBeats,
                        beat_value: 4
                    });
                    voice.addTickables(notes);

                    // Format and justify the notes with optimized spacing
                    const formatWidth = Math.max(150, staveWidth - 60);
                    new Formatter().joinVoices([voice]).format([voice], formatWidth);

                    // Render voice
                    voice.draw(context, stave);

                    console.log('Notes rendered successfully');
                } else {
                    console.log('No valid notes to render');
                }
                ` : `
                console.log('No notes to display');
                `}

            } catch (error) {
                console.error('VexFlow error:', error);
                document.getElementById('notation').innerHTML =
                    '<div class="error">Error loading music notation: ' + error.message + '</div>';
            }
        </script>
    </body>
    </html>
    `;
  };

  // Fallback component if there's an error
  if (webViewError) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>
          Music notation unavailable: {webViewError}
        </Text>
      </View>
    );
  }

  // Use direct VexFlow rendering for web platform
  if (Platform.OS === 'web') {
    return (
      <View style={styles.container}>
        <div
          ref={notationRef}
          style={{
            width: width,
            height: height,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        />
        {!vexFlowLoaded && (
          <Text style={styles.loadingText}>Loading music notation...</Text>
        )}
      </View>
    );
  }

  // Use WebView for mobile platforms
  return (
    <View style={styles.container}>
      <WebView
        source={{ html: generateVexFlowHTML() }}
        style={{ width, height }}
        scrollEnabled={false}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
        onError={(syntheticEvent) => {
          const { nativeEvent } = syntheticEvent;
          console.warn('WebView error: ', nativeEvent);
          setWebViewError(`WebView error: ${nativeEvent.description || 'Unknown error'}`);
        }}
        onHttpError={(syntheticEvent) => {
          const { nativeEvent } = syntheticEvent;
          console.warn('WebView HTTP error: ', nativeEvent);
          setWebViewError(`HTTP error: ${nativeEvent.statusCode}`);
        }}
        onLoadEnd={() => {
          console.log('WebView loaded successfully');
        }}
        onMessage={(event) => {
          console.log('WebView message:', event.nativeEvent.data);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    marginHorizontal: 15,
    marginVertical: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 7,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    alignSelf: 'center', // Center the staff container
    maxWidth: 600, // Maximum width for larger screens
  },
  errorText: {
    color: '#dc3545',
    fontSize: 16,
    textAlign: 'center',
    padding: 20,
  },
  loadingText: {
    color: '#666',
    fontSize: 14,
    textAlign: 'center',
    padding: 10,
  },
});

export default StaffNotation;
