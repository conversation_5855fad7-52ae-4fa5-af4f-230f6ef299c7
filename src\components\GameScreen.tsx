import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert, Dimensions, Modal } from 'react-native';
import StaffNotation from './StaffNotation';
import NoteInput from './NoteInput';
import { GameSettings, GameState, Note, StaffDimensions } from '../types/music';
import { generateNoteSequence } from '../utils/noteGenerator';
import { validateNoteAnswer, getNoteDisplayName } from '../utils/musicTheory';

interface GameScreenProps {
  settings: GameSettings;
  onGameEnd: (score: number, total: number) => void;
}

const { width: screenWidth } = Dimensions.get('window');

const GameScreen: React.FC<GameScreenProps> = ({ settings, onGameEnd }) => {
  const [gameState, setGameState] = useState<GameState>({
    currentNotes: [],
    userAnswers: [],
    score: 0,
    totalQuestions: 0,
    isShowingNotes: false,
    isGameActive: false,
    timeRemaining: 0,
  });

  const [showResults, setShowResults] = useState(false);
  const [roundResults, setRoundResults] = useState({
    correctCount: 0,
    totalCount: 0,
    percentage: 0,
    correctAnswers: [] as string[],
    userAnswers: [] as string[],
  });

  const [notesCollapsed, setNotesCollapsed] = useState(false);
  const [showNotesTemporarily, setShowNotesTemporarily] = useState(false);
  
  // Calculate optimal staff width based on number of notes
  const calculateStaffDimensions = (noteCount: number): StaffDimensions => {
    const baseWidth = 120; // Width for treble clef and margins
    const noteWidth = 60;  // Width per note (more compact)
    const maxWidth = Math.min(screenWidth - 40, 500); // Maximum width limit

    const calculatedWidth = baseWidth + (noteCount * noteWidth);
    const finalWidth = Math.min(calculatedWidth, maxWidth);

    return {
      width: finalWidth,
      height: 180, // Slightly smaller height
      lineSpacing: 12, // Tighter line spacing
      noteSpacing: noteWidth, // Consistent with calculation
    };
  };

  const staffDimensions = calculateStaffDimensions(gameState.currentNotes.length);
  
  const startNewRound = useCallback(() => {
    const newNotes = generateNoteSequence(settings);

    // Debug: Log the notes that should be guessed
    console.log('=== NEUE RUNDE ===');
    console.log('Zu ratende Noten:', newNotes.map((note, index) => ({
      nummer: index + 1,
      note: getNoteDisplayName(note),
      details: note
    })));

    setNotesCollapsed(false);
    setShowNotesTemporarily(false);

    setGameState(prev => ({
      ...prev,
      currentNotes: newNotes,
      userAnswers: [],
      isShowingNotes: true,
      isGameActive: true,
      timeRemaining: settings.displayTime,
    }));
  }, [settings]);
  
  const handleAnswerSubmit = useCallback((answers: string[]) => {
    const { currentNotes } = gameState;
    let correctCount = 0;

    // Debug: Log the submitted answers
    console.log('=== ANTWORTEN EINGEREICHT ===');
    console.log('Gewählte Antworten:', answers);
    console.log('Korrekte Antworten:', currentNotes.map(note => getNoteDisplayName(note)));

    const detailledResults: any[] = [];

    answers.forEach((answer, index) => {
      if (index < currentNotes.length) {
        const isCorrect = validateNoteAnswer(answer, currentNotes[index]);
        const correctAnswer = getNoteDisplayName(currentNotes[index]);

        detailledResults.push({
          nummer: index + 1,
          eingegeben: answer,
          korrekt: correctAnswer,
          richtig: isCorrect,
          enharmonisch: answer !== correctAnswer && isCorrect
        });

        if (isCorrect) {
          correctCount++;
        }
      }
    });

    console.log('Detaillierte Ergebnisse:', detailledResults);
    console.log(`Richtige Antworten: ${correctCount}/${currentNotes.length}`);

    const newScore = gameState.score + correctCount;
    const newTotal = gameState.totalQuestions + currentNotes.length;

    setGameState(prev => ({
      ...prev,
      userAnswers: answers,
      score: newScore,
      totalQuestions: newTotal,
      isGameActive: false,
    }));

    // Prepare results for modal
    const percentage = Math.round((correctCount / currentNotes.length) * 100);
    const correctAnswers = currentNotes.map(note => getNoteDisplayName(note));

    setRoundResults({
      correctCount,
      totalCount: currentNotes.length,
      percentage,
      correctAnswers,
      userAnswers: answers,
    });

    setShowResults(true);
  }, [gameState]);

  const handleNextRound = () => {
    setShowResults(false);
    startNewRound();
  };

  const handleEndGame = () => {
    setShowResults(false);
    onGameEnd(gameState.score, gameState.totalQuestions);
  };

  const handleShowNotesTemporarily = () => {
    setShowNotesTemporarily(true);
    // Hide again after 3 seconds
    setTimeout(() => {
      setShowNotesTemporarily(false);
    }, 3000);
  };
  
  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (gameState.isShowingNotes && gameState.timeRemaining > 0) {
      interval = setInterval(() => {
        setGameState(prev => {
          const newTimeRemaining = prev.timeRemaining - 0.1;

          // If time is up, collapse notes instead of hiding them
          if (newTimeRemaining <= 0) {
            setNotesCollapsed(true);
            return {
              ...prev,
              timeRemaining: 0,
              isShowingNotes: false,
            };
          }

          return {
            ...prev,
            timeRemaining: newTimeRemaining,
          };
        });
      }, 100);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [gameState.isShowingNotes, gameState.timeRemaining]);
  
  // Start first round on mount
  useEffect(() => {
    startNewRound();
  }, [startNewRound]);
  
  const formatTime = (time: number): string => {
    return time.toFixed(1);
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.scoreText}>
          Score: {gameState.score}/{gameState.totalQuestions}
        </Text>
        {gameState.isShowingNotes && (
          <Text style={styles.timerText}>
            Time: {formatTime(gameState.timeRemaining)}s
          </Text>
        )}
      </View>
      
      <StaffNotation
        notes={gameState.currentNotes}
        dimensions={notesCollapsed && !showNotesTemporarily ? {
          ...staffDimensions,
          width: staffDimensions.width * 0.6,
          height: staffDimensions.height * 0.6
        } : staffDimensions}
        showNotes={gameState.isShowingNotes || showNotesTemporarily}
      />

      {notesCollapsed && !showNotesTemporarily && (
        <TouchableOpacity
          style={styles.showNotesButton}
          onPress={handleShowNotesTemporarily}
        >
          <Text style={styles.showNotesButtonText}>
            🔍 Noten kurz anzeigen (3s)
          </Text>
        </TouchableOpacity>
      )}
      
      {gameState.isShowingNotes ? (
        <View style={styles.instructionContainer}>
          <Text style={styles.instructionText}>
            Memorize the notes shown above!
          </Text>
          <Text style={styles.subInstructionText}>
            Time remaining: {formatTime(gameState.timeRemaining)} seconds
          </Text>
        </View>
      ) : (
        <NoteInput
          expectedNotes={gameState.currentNotes}
          onSubmit={handleAnswerSubmit}
          disabled={!gameState.isGameActive}
        />
      )}
      
      <TouchableOpacity
        style={styles.endGameButton}
        onPress={() => onGameEnd(gameState.score, gameState.totalQuestions)}
      >
        <Text style={styles.endGameButtonText}>End Game</Text>
      </TouchableOpacity>

      {/* Results Modal */}
      <Modal
        visible={showResults}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowResults(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Round Results</Text>

            <View style={styles.scoreContainer}>
              <Text style={styles.scoreText}>
                {roundResults.correctCount} out of {roundResults.totalCount} correct
              </Text>
              <Text style={styles.percentageText}>
                {roundResults.percentage}%
              </Text>
            </View>

            <View style={styles.answersContainer}>
              <Text style={styles.answersTitle}>Answers:</Text>
              {roundResults.correctAnswers.map((correctAnswer, index) => (
                <View key={index} style={styles.answerRow}>
                  <Text style={styles.answerNumber}>{index + 1}.</Text>
                  <Text style={styles.correctAnswer}>
                    {correctAnswer}
                  </Text>
                  <Text style={styles.userAnswer}>
                    (You: {roundResults.userAnswers[index] || 'No answer'})
                  </Text>
                  <Text style={styles.resultIcon}>
                    {validateNoteAnswer(roundResults.userAnswers[index] || '', gameState.currentNotes[index]) ? '✅' : '❌'}
                  </Text>
                </View>
              ))}
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.nextRoundButton}
                onPress={handleNextRound}
              >
                <Text style={styles.nextRoundButtonText}>Next Round</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.endGameModalButton}
                onPress={handleEndGame}
              >
                <Text style={styles.endGameModalButtonText}>End Game</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    padding: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderRadius: 10,
    marginBottom: 10,
  },
  scoreText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  timerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  instructionContainer: {
    padding: 20,
    backgroundColor: '#fff',
    borderRadius: 10,
    margin: 10,
    alignItems: 'center',
  },
  instructionText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 10,
  },
  subInstructionText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  endGameButton: {
    backgroundColor: '#dc3545',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 8,
    marginTop: 20,
    alignSelf: 'center',
  },
  endGameButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 25,
    margin: 20,
    maxWidth: 400,
    width: '90%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 20,
  },
  scoreContainer: {
    alignItems: 'center',
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
  },
  percentageText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#28a745',
    marginTop: 5,
  },
  answersContainer: {
    marginBottom: 20,
  },
  answersTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  answerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 10,
    marginVertical: 2,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  answerNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
    width: 25,
  },
  correctAnswer: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  userAnswer: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  resultIcon: {
    fontSize: 18,
    width: 30,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  nextRoundButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    flex: 1,
  },
  nextRoundButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  endGameModalButton: {
    backgroundColor: '#dc3545',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    flex: 1,
  },
  endGameModalButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  showNotesButton: {
    backgroundColor: '#6f42c1',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
    marginTop: 10,
    alignSelf: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  showNotesButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default GameScreen;
