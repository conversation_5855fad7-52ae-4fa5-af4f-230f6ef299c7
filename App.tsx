import React, { useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, TouchableOpacity, Alert, SafeAreaView } from 'react-native';
import GameScreen from './src/components/GameScreen';
import { GameSettings } from './src/types/music';

type AppState = 'menu' | 'game' | 'results';

export default function App() {
  const [appState, setAppState] = useState<AppState>('menu');
  const [gameSettings, setGameSettings] = useState<GameSettings>({
    displayTime: 3,
    numberOfNotes: 2,
    includeAccidentals: false,
    clef: 'treble',
    difficulty: 'easy',
  });
  const [finalScore, setFinalScore] = useState({ score: 0, total: 0 });

  const startGame = (settings: GameSettings) => {
    setGameSettings(settings);
    setAppState('game');
  };

  const handleGameEnd = (score: number, total: number) => {
    setFinalScore({ score, total });
    setAppState('results');
  };

  const resetToMenu = () => {
    setAppState('menu');
  };

  const renderMenu = () => (
    <View style={styles.menuContainer}>
      <Text style={styles.title}>Piano Note Learner</Text>
      <Text style={styles.subtitle}>Learn to read musical notes!</Text>

      <View style={styles.difficultyContainer}>
        <Text style={styles.sectionTitle}>Choose Difficulty:</Text>

        <TouchableOpacity
          style={styles.difficultyButton}
          onPress={() => startGame({
            ...gameSettings,
            difficulty: 'easy',
            displayTime: 4,
            numberOfNotes: 1,
            includeAccidentals: false,
          })}
        >
          <Text style={styles.buttonText}>Easy</Text>
          <Text style={styles.buttonSubtext}>1 note, 4 seconds, no accidentals</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.difficultyButton}
          onPress={() => startGame({
            ...gameSettings,
            difficulty: 'medium',
            displayTime: 3,
            numberOfNotes: 2,
            includeAccidentals: false,
          })}
        >
          <Text style={styles.buttonText}>Medium</Text>
          <Text style={styles.buttonSubtext}>2 notes, 3 seconds, no accidentals</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.difficultyButton}
          onPress={() => startGame({
            ...gameSettings,
            difficulty: 'hard',
            displayTime: 2,
            numberOfNotes: 3,
            includeAccidentals: true,
          })}
        >
          <Text style={styles.buttonText}>Hard</Text>
          <Text style={styles.buttonSubtext}>3 notes, 2 seconds, with accidentals</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.difficultyButton, { backgroundColor: '#6f42c1' }]}
          onPress={() => startGame({
            ...gameSettings,
            difficulty: 'easy',
            displayTime: 5,
            numberOfNotes: 1,
            includeAccidentals: true,
          })}
        >
          <Text style={styles.buttonText}>Test Accidentals</Text>
          <Text style={styles.buttonSubtext}>1 note, 5 seconds, always with accidentals</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderResults = () => {
    const percentage = finalScore.total > 0 ? Math.round((finalScore.score / finalScore.total) * 100) : 0;

    return (
      <View style={styles.resultsContainer}>
        <Text style={styles.title}>Game Results</Text>
        <Text style={styles.scoreText}>
          Final Score: {finalScore.score} / {finalScore.total}
        </Text>
        <Text style={styles.percentageText}>
          Accuracy: {percentage}%
        </Text>

        <TouchableOpacity style={styles.playAgainButton} onPress={resetToMenu}>
          <Text style={styles.buttonText}>Play Again</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      {appState === 'menu' && renderMenu()}
      {appState === 'game' && (
        <GameScreen
          settings={gameSettings}
          onGameEnd={handleGameEnd}
        />
      )}
      {appState === 'results' && renderResults()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f0f0',
  },
  menuContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    color: '#666',
    marginBottom: 40,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  difficultyContainer: {
    width: '100%',
    maxWidth: 300,
  },
  difficultyButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 20,
    paddingHorizontal: 30,
    borderRadius: 15,
    marginBottom: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  buttonText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  buttonSubtext: {
    color: '#fff',
    fontSize: 14,
    opacity: 0.9,
    textAlign: 'center',
  },
  resultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  scoreText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  percentageText: {
    fontSize: 20,
    color: '#666',
    marginBottom: 40,
  },
  playAgainButton: {
    backgroundColor: '#28a745',
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 10,
  },
});
