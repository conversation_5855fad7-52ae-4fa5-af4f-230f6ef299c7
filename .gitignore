# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# React Native
# React Native CLI
android/
ios/
.buckconfig
.flowconfig
.watchmanconfig

# Metro
.metro-health-check*
.metro-cache/

# Native builds
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.keystore
!debug.keystore

# Kotlin
.kotlin/

# Gradle (Android)
.gradle/
build/
gradle-wrapper.jar
gradle-wrapper.properties
local.properties

# Xcode (iOS)
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/
*.xccheckout
*.moved-aside
DerivedData/
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace/

# CocoaPods (iOS)
Pods/
*.xcworkspace
!default.xcworkspace

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local

# TypeScript
*.tsbuildinfo
*.d.ts.map

# Testing
coverage/
.nyc_output/
junit.xml

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache/
.parcel-cache/

# Next.js build output
.next/
out/

# Nuxt.js build / generate output
.nuxt/

# Gatsby files
.cache/
public/

# Storybook build outputs
.out/
.storybook-out/

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.pem

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar

# Local configuration files
config.local.js
config.local.json

# Build artifacts
build/
dist/
lib/

# Documentation
docs/build/

# Sentry
.sentryclirc

# Bundle artifacts
*.bundle

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log

# Flipper
flipper-rn-addons.podspec.json

# Ruby (for React Native)
vendor/
.bundle/

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/**/*.png
fastlane/test_output

# EAS (Expo Application Services)
.easignore

# Temporary files
.tmp/
