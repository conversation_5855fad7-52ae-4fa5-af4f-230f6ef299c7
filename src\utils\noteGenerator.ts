import { Note, NoteName, Accidental, GameSettings } from '../types/music';
import { NOTE_NAMES, TREBLE_CLEF_POSITIONS, getPositionForNote } from './musicTheory';

export function generateRandomNote(settings: GameSettings): Note {
  const { includeAccidentals, difficulty } = settings;
  
  // Define note ranges based on difficulty
  const noteRanges = {
    easy: ['C4', 'D4', 'E4', 'F4', 'G4', 'A4', 'B4', 'C5'],
    medium: ['C4', 'D4', 'E4', 'F4', 'G4', 'A4', 'B4', 'C5', 'D5', 'E5', 'F5', 'G5'],
    hard: ['C4', 'D4', 'E4', 'F4', 'G4', 'A4', 'B4', 'C5', 'D5', 'E5', 'F5', 'G5', 'A5', 'B5', 'C6']
  };
  
  const availableNotes = noteRanges[difficulty];
  const randomNoteString = availableNotes[Math.floor(Math.random() * availableNotes.length)];
  
  // Parse note string (e.g., "C4" -> name: "C", octave: 4)
  const noteName = randomNoteString.charAt(0) as NoteName;
  const octave = parseInt(randomNoteString.slice(1));
  
  // Determine accidental
  let accidental: Accidental = 'natural';
  if (includeAccidentals) {
    // Higher chance of accidental when enabled (70% chance)
    const rand = Math.random();
    if (rand < 0.7) {
      accidental = Math.random() < 0.5 ? 'sharp' : 'flat';
    }
  }
  
  const position = getPositionForNote(randomNoteString);

  const generatedNote = {
    name: noteName,
    accidental,
    octave,
    position
  };



  return generatedNote;
}

export function generateNoteSequence(settings: GameSettings): Note[] {
  const notes: Note[] = [];
  const { numberOfNotes } = settings;

  console.log('=== NOTENGENERIERUNG ===');
  console.log('Einstellungen:', {
    anzahlNoten: numberOfNotes,
    schwierigkeit: settings.difficulty,
    vorzeichen: settings.includeAccidentals,
    anzeigezeit: settings.displayTime
  });

  for (let i = 0; i < numberOfNotes; i++) {
    const note = generateRandomNote(settings);
    notes.push(note);
    console.log(`Note ${i + 1} generiert:`, {
      name: note.name,
      vorzeichen: note.accidental,
      oktave: note.octave,
      anzeigename: `${note.name}${note.accidental === 'sharp' ? '♯' : note.accidental === 'flat' ? '♭' : ''}${note.octave}`
    });
  }

  return notes;
}

export function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

export function getRandomNoteNames(count: number, includeCorrect: Note[]): string[] {
  const allNoteNames = NOTE_NAMES.flatMap(name => 
    ['natural', 'sharp', 'flat'].map(acc => 
      acc === 'natural' ? name : `${name}${acc === 'sharp' ? '♯' : '♭'}`
    )
  );
  
  const correctNames = includeCorrect.map(note => 
    note.accidental === 'natural' ? note.name : 
    `${note.name}${note.accidental === 'sharp' ? '♯' : '♭'}`
  );
  
  const incorrectNames = allNoteNames.filter(name => !correctNames.includes(name));
  const randomIncorrect = shuffleArray(incorrectNames).slice(0, count - correctNames.length);
  
  return shuffleArray([...correctNames, ...randomIncorrect]);
}
